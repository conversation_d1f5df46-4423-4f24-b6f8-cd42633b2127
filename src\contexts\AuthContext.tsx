'use client'

import React, { createContext, useContext, useEffect, useState } from 'react'
import { onAuthStateChange, type AuthUser } from '@/lib/auth'
import { mockGetCurrentUser, type MockUser } from '@/lib/mockAuth'

interface AuthContextType {
  user: AuthUser | MockUser | null
  loading: boolean
  isAdmin: boolean
  isStudent: boolean
  refreshUser: () => void
}

const AuthContext = createContext<AuthContextType>({
  user: null,
  loading: true,
  isAdmin: false,
  isStudent: false,
  refreshUser: () => {}
})

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

interface AuthProviderProps {
  children: React.ReactNode
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<AuthUser | MockUser | null>(null)
  const [loading, setLoading] = useState(true)

  const refreshUser = () => {
    const mockUser = mockGetCurrentUser()
    setUser(mockUser)
    setLoading(false)
  }

  useEffect(() => {
    refreshUser()
  }, [])

  // Listen for storage changes (when user logs in/out in another tab)
  useEffect(() => {
    const handleStorageChange = () => {
      refreshUser()
    }

    window.addEventListener('storage', handleStorageChange)
    return () => window.removeEventListener('storage', handleStorageChange)
  }, [])

  const value: AuthContextType = {
    user,
    loading,
    isAdmin: user?.role === 'admin',
    isStudent: user?.role === 'student',
    refreshUser
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}
