'use client'

import { useState } from 'react'
import ProtectedRoute from '@/components/ProtectedRoute'
import Layout from '@/components/layout/Layout'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import Input from '@/components/ui/Input'
import FileUpload from '@/components/ui/FileUpload'
import { useAuth } from '@/contexts/AuthContext'
import { 
  UserIcon,
  CameraIcon,
  KeyIcon,
  BellIcon
} from '@heroicons/react/24/outline'
import toast from 'react-hot-toast'

interface ProfileFormData {
  fullName: string
  email: string
  bio: string
  phone: string
  website: string
  avatar: File | null
}

interface PasswordFormData {
  currentPassword: string
  newPassword: string
  confirmPassword: string
}

export default function ProfilePage() {
  const { user } = useAuth()
  const [profileData, setProfileData] = useState<ProfileFormData>({
    fullName: user?.full_name || (user as any)?.displayName || '',
    email: user?.email || '',
    bio: '',
    phone: '',
    website: '',
    avatar: null
  })
  const [passwordData, setPasswordData] = useState<PasswordFormData>({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  })
  const [loading, setLoading] = useState(false)
  const [passwordLoading, setPasswordLoading] = useState(false)

  const handleProfileChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setProfileData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setPasswordData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  const handleAvatarSelect = (files: File[]) => {
    if (files.length > 0) {
      setProfileData(prev => ({
        ...prev,
        avatar: files[0]
      }))
    }
  }

  const handleProfileSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      // TODO: Implement profile update logic
      console.log('Updating profile:', profileData)
      
      toast.success('تم تحديث الملف الشخصي بنجاح!')
      
    } catch (error) {
      toast.error('حدث خطأ أثناء تحديث الملف الشخصي')
    } finally {
      setLoading(false)
    }
  }

  const handlePasswordSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setPasswordLoading(true)

    try {
      // Validation
      if (passwordData.newPassword !== passwordData.confirmPassword) {
        toast.error('كلمات المرور الجديدة غير متطابقة')
        return
      }

      if (passwordData.newPassword.length < 6) {
        toast.error('كلمة المرور الجديدة يجب أن تكون 6 أحرف على الأقل')
        return
      }

      // TODO: Implement password change logic
      console.log('Changing password')
      
      toast.success('تم تغيير كلمة المرور بنجاح!')
      setPasswordData({
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      })
      
    } catch (error) {
      toast.error('حدث خطأ أثناء تغيير كلمة المرور')
    } finally {
      setPasswordLoading(false)
    }
  }

  return (
    <ProtectedRoute requiredRole="admin">
      <Layout>
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-2xl font-bold text-primary-900">
              الملف الشخصي
            </h1>
            <p className="text-secondary-600">
              إدارة معلوماتك الشخصية وإعدادات الحساب
            </p>
          </div>

          <div className="space-y-6">
            {/* Profile Information */}
            <Card>
              <Card.Header>
                <div className="flex items-center gap-2">
                  <UserIcon className="h-5 w-5 text-primary-600" />
                  <Card.Title>المعلومات الشخصية</Card.Title>
                </div>
              </Card.Header>
              <Card.Content>
                <form onSubmit={handleProfileSubmit} className="space-y-6">
                  {/* Avatar Upload */}
                  <div className="flex items-center gap-6">
                    <div className="h-20 w-20 bg-primary-100 rounded-full flex items-center justify-center">
                      {(user as any)?.photoURL ? (
                        <img
                          src={(user as any).photoURL}
                          alt="Profile"
                          className="h-20 w-20 rounded-full object-cover"
                        />
                      ) : (
                        <UserIcon className="h-10 w-10 text-primary-600" />
                      )}
                    </div>
                    <div className="flex-1">
                      <FileUpload
                        label="صورة الملف الشخصي"
                        description="اختر صورة شخصية (PNG, JPG - حتى 2MB)"
                        accept={{
                          'image/*': ['.png', '.jpg', '.jpeg']
                        }}
                        maxSize={2 * 1024 * 1024} // 2MB
                        onFileSelect={handleAvatarSelect}
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <Input
                      label="الاسم الكامل *"
                      name="fullName"
                      value={profileData.fullName}
                      onChange={handleProfileChange}
                      required
                    />

                    <Input
                      label="البريد الإلكتروني *"
                      name="email"
                      type="email"
                      value={profileData.email}
                      onChange={handleProfileChange}
                      required
                      disabled
                      helperText="لا يمكن تغيير البريد الإلكتروني"
                    />

                    <Input
                      label="رقم الهاتف"
                      name="phone"
                      value={profileData.phone}
                      onChange={handleProfileChange}
                      placeholder="+966 50 123 4567"
                    />

                    <Input
                      label="الموقع الإلكتروني"
                      name="website"
                      value={profileData.website}
                      onChange={handleProfileChange}
                      placeholder="https://example.com"
                    />
                  </div>

                  <div>
                    <label className="form-label">نبذة شخصية</label>
                    <textarea
                      name="bio"
                      value={profileData.bio}
                      onChange={handleProfileChange}
                      rows={4}
                      className="form-input"
                      placeholder="اكتب نبذة مختصرة عنك وخبراتك التعليمية"
                    />
                  </div>

                  <div className="flex justify-end">
                    <Button type="submit" loading={loading}>
                      حفظ التغييرات
                    </Button>
                  </div>
                </form>
              </Card.Content>
            </Card>

            {/* Change Password */}
            <Card>
              <Card.Header>
                <div className="flex items-center gap-2">
                  <KeyIcon className="h-5 w-5 text-primary-600" />
                  <Card.Title>تغيير كلمة المرور</Card.Title>
                </div>
              </Card.Header>
              <Card.Content>
                <form onSubmit={handlePasswordSubmit} className="space-y-4">
                  <Input
                    label="كلمة المرور الحالية *"
                    name="currentPassword"
                    type="password"
                    value={passwordData.currentPassword}
                    onChange={handlePasswordChange}
                    required
                  />

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <Input
                      label="كلمة المرور الجديدة *"
                      name="newPassword"
                      type="password"
                      value={passwordData.newPassword}
                      onChange={handlePasswordChange}
                      required
                      helperText="6 أحرف على الأقل"
                    />

                    <Input
                      label="تأكيد كلمة المرور الجديدة *"
                      name="confirmPassword"
                      type="password"
                      value={passwordData.confirmPassword}
                      onChange={handlePasswordChange}
                      required
                    />
                  </div>

                  <div className="flex justify-end">
                    <Button type="submit" loading={passwordLoading}>
                      تغيير كلمة المرور
                    </Button>
                  </div>
                </form>
              </Card.Content>
            </Card>

            {/* Notification Settings */}
            <Card>
              <Card.Header>
                <div className="flex items-center gap-2">
                  <BellIcon className="h-5 w-5 text-primary-600" />
                  <Card.Title>إعدادات الإشعارات</Card.Title>
                </div>
              </Card.Header>
              <Card.Content>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium text-primary-900">
                        إشعارات البريد الإلكتروني
                      </h4>
                      <p className="text-sm text-secondary-600">
                        تلقي إشعارات عند تسجيل طلاب جدد أو إكمال الكورسات
                      </p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input type="checkbox" className="sr-only peer" defaultChecked />
                      <div className="w-11 h-6 bg-secondary-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-secondary-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
                    </label>
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium text-primary-900">
                        إشعارات المنصة
                      </h4>
                      <p className="text-sm text-secondary-600">
                        تلقي إشعارات داخل المنصة عن النشاطات المهمة
                      </p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input type="checkbox" className="sr-only peer" defaultChecked />
                      <div className="w-11 h-6 bg-secondary-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-secondary-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
                    </label>
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium text-primary-900">
                        تقارير أسبوعية
                      </h4>
                      <p className="text-sm text-secondary-600">
                        تلقي تقرير أسبوعي عن أداء الطلاب والكورسات
                      </p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input type="checkbox" className="sr-only peer" />
                      <div className="w-11 h-6 bg-secondary-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-secondary-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
                    </label>
                  </div>
                </div>
              </Card.Content>
            </Card>
          </div>
        </div>
      </Layout>
    </ProtectedRoute>
  )
}
