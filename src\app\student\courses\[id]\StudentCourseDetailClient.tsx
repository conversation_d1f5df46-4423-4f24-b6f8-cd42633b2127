// This is the client component file - keeping it as is

'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import ProtectedRoute from '@/components/ProtectedRoute'
import Layout from '@/components/layout/Layout'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import { 
  ArrowLeftIcon,
  PlayIcon,
  PauseIcon,
  DocumentArrowDownIcon,
  CheckCircleIcon,
  ClockIcon,
  BookOpenIcon
} from '@heroicons/react/24/outline'

interface StudentCourseDetailClientProps {
  courseId: string
}

export default function StudentCourseDetailClient({ courseId }: StudentCourseDetailClientProps) {
  const router = useRouter()
  const [currentVideo, setCurrentVideo] = useState<string | null>(null)
  const [isPlaying, setIsPlaying] = useState(false)
  const [completedVideos, setCompletedVideos] = useState<string[]>(['1'])

  // Mock data - will be replaced with real data from database
  const course = {
    id: courseId,
    title: 'مقدمة في البرمجة',
    description: 'تعلم أساسيات البرمجة باستخدام JavaScript',
    instructor: 'أحمد محمد',
    progress: 75,
    sections: [
      {
        id: '1',
        title: 'الأساسيات',
        videos: [
          {
            id: '1',
            title: 'مقدمة عن البرمجة',
            description: 'فيديو تعريفي عن البرمجة وأهميتها',
            videoUrl: 'https://example.com/video1.mp4',
            duration: 600,
            orderIndex: 1,
            pdfs: [
              {
                id: '1',
                title: 'ملاحظات الدرس الأول',
                fileUrl: 'https://example.com/notes1.pdf',
                fileSize: 1024000
              }
            ]
          },
          {
            id: '2',
            title: 'إعداد بيئة التطوير',
            description: 'كيفية إعداد بيئة التطوير للبرمجة',
            videoUrl: 'https://example.com/video2.mp4',
            duration: 900,
            orderIndex: 2,
            pdfs: []
          }
        ]
      },
      {
        id: '2',
        title: 'المتغيرات والثوابت',
        videos: [
          {
            id: '3',
            title: 'أنواع المتغيرات',
            description: 'تعلم أنواع المتغيرات المختلفة',
            videoUrl: 'https://example.com/video3.mp4',
            duration: 750,
            orderIndex: 1,
            pdfs: [
              {
                id: '2',
                title: 'أمثلة على المتغيرات',
                fileUrl: 'https://example.com/variables.pdf',
                fileSize: 2048000
              }
            ]
          }
        ]
      }
    ]
  }

  const formatDuration = (seconds: number) => {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const handleVideoPlay = (videoId: string) => {
    setCurrentVideo(videoId)
    setIsPlaying(true)
  }

  const handleVideoComplete = (videoId: string) => {
    if (!completedVideos.includes(videoId)) {
      setCompletedVideos([...completedVideos, videoId])
    }
  }

  const getTotalVideos = () => {
    return course.sections.reduce((total, section) => total + section.videos.length, 0)
  }

  const getCompletedCount = () => {
    return completedVideos.length
  }

  const getProgressPercentage = () => {
    const total = getTotalVideos()
    const completed = getCompletedCount()
    return total > 0 ? Math.round((completed / total) * 100) : 0
  }

  return (
    <ProtectedRoute requiredRole="student">
      <Layout>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Header */}
          <div className="mb-8">
            <div className="flex items-center mb-4">
              <Button
                variant="outline"
                onClick={() => router.back()}
                className="flex items-center space-x-2 rtl:space-x-reverse mr-4"
              >
                <ArrowLeftIcon className="h-4 w-4" />
                <span>العودة</span>
              </Button>
              <div className="flex-1">
                <h1 className="text-2xl font-bold text-primary-900">{course.title}</h1>
                <p className="text-secondary-600">{course.description}</p>
                <p className="text-sm text-secondary-500 mt-1">المدرس: {course.instructor}</p>
              </div>
            </div>

            {/* Progress Bar */}
            <Card>
              <div className="flex items-center justify-between mb-4">
                <div>
                  <h3 className="text-lg font-semibold text-primary-900">تقدمك في الكورس</h3>
                  <p className="text-secondary-600">
                    {getCompletedCount()} من {getTotalVideos()} فيديو مكتمل
                  </p>
                </div>
                <div className="text-right">
                  <div className="text-2xl font-bold text-primary-600">
                    {getProgressPercentage()}%
                  </div>
                </div>
              </div>
              <div className="w-full bg-secondary-200 rounded-full h-2">
                <div 
                  className="bg-primary-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${getProgressPercentage()}%` }}
                ></div>
              </div>
            </Card>
          </div>

          {/* Course Content */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Video Player */}
            <div className="lg:col-span-2">
              <Card>
                <div className="aspect-video bg-black rounded-lg flex items-center justify-center">
                  {currentVideo ? (
                    <div className="text-white text-center">
                      <PlayIcon className="h-16 w-16 mx-auto mb-4" />
                      <p>مشغل الفيديو</p>
                      <p className="text-sm opacity-75">فيديو ID: {currentVideo}</p>
                    </div>
                  ) : (
                    <div className="text-white text-center">
                      <BookOpenIcon className="h-16 w-16 mx-auto mb-4 opacity-50" />
                      <p>اختر فيديو للبدء</p>
                    </div>
                  )}
                </div>
              </Card>
            </div>

            {/* Course Sections */}
            <div className="space-y-6">
              <h2 className="text-xl font-bold text-primary-900">محتوى الكورس</h2>
              
              {course.sections.map((section) => (
                <Card key={section.id}>
                  <Card.Header>
                    <Card.Title className="text-lg">{section.title}</Card.Title>
                  </Card.Header>
                  
                  <Card.Content>
                    <div className="space-y-3">
                      {section.videos.map((video) => {
                        const isCompleted = completedVideos.includes(video.id)
                        const isCurrent = currentVideo === video.id
                        
                        return (
                          <div 
                            key={video.id}
                            className={`p-3 rounded-lg border cursor-pointer transition-all ${
                              isCurrent 
                                ? 'border-primary-500 bg-primary-50' 
                                : 'border-secondary-200 hover:border-secondary-300'
                            }`}
                            onClick={() => handleVideoPlay(video.id)}
                          >
                            <div className="flex items-center justify-between">
                              <div className="flex items-center space-x-3 rtl:space-x-reverse">
                                <div className={`p-2 rounded-full ${
                                  isCompleted 
                                    ? 'bg-green-100 text-green-600' 
                                    : isCurrent 
                                    ? 'bg-primary-100 text-primary-600'
                                    : 'bg-secondary-100 text-secondary-600'
                                }`}>
                                  {isCompleted ? (
                                    <CheckCircleIcon className="h-4 w-4" />
                                  ) : (
                                    <PlayIcon className="h-4 w-4" />
                                  )}
                                </div>
                                <div>
                                  <h4 className="font-medium text-primary-900">{video.title}</h4>
                                  <div className="flex items-center space-x-2 rtl:space-x-reverse text-sm text-secondary-500">
                                    <ClockIcon className="h-3 w-3" />
                                    <span>{formatDuration(video.duration)}</span>
                                  </div>
                                </div>
                              </div>
                            </div>
                            
                            {video.description && (
                              <p className="text-sm text-secondary-600 mt-2 mr-10">
                                {video.description}
                              </p>
                            )}

                            {/* PDFs */}
                            {video.pdfs && video.pdfs.length > 0 && (
                              <div className="mt-3 mr-10">
                                <h5 className="text-xs font-medium text-secondary-700 mb-2">
                                  المواد التعليمية:
                                </h5>
                                <div className="space-y-1">
                                  {video.pdfs.map((pdf) => (
                                    <div 
                                      key={pdf.id} 
                                      className="flex items-center justify-between bg-secondary-50 p-2 rounded text-xs"
                                    >
                                      <div className="flex items-center space-x-2 rtl:space-x-reverse">
                                        <DocumentArrowDownIcon className="h-3 w-3 text-red-500" />
                                        <span>{pdf.title}</span>
                                        <span className="text-secondary-500">
                                          ({formatFileSize(pdf.fileSize)})
                                        </span>
                                      </div>
                                      <Button 
                                        variant="outline" 
                                        size="sm"
                                        className="text-xs py-1 px-2"
                                        onClick={(e) => {
                                          e.stopPropagation()
                                          window.open(pdf.fileUrl, '_blank')
                                        }}
                                      >
                                        تحميل
                                      </Button>
                                    </div>
                                  ))}
                                </div>
                              </div>
                            )}
                          </div>
                        )
                      })}
                    </div>
                  </Card.Content>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </Layout>
    </ProtectedRoute>
  )
}
