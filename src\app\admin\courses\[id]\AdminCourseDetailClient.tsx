'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import ProtectedRoute from '@/components/ProtectedRoute'
import Layout from '@/components/layout/Layout'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import Modal from '@/components/ui/Modal'
import FileUpload from '@/components/ui/FileUpload'
import { 
  ArrowLeftIcon,
  PlusIcon,
  VideoCameraIcon,
  DocumentTextIcon,
  PencilIcon,
  TrashIcon,
  PlayIcon,
  EyeIcon
} from '@heroicons/react/24/outline'

interface AdminCourseDetailClientProps {
  courseId: string
}

export default function AdminCourseDetailClient({ courseId }: AdminCourseDetailClientProps) {
  const router = useRouter()
  const [showAddSectionModal, setShowAddSectionModal] = useState(false)
  const [showAddVideoModal, setShowAddVideoModal] = useState(false)
  const [showAddPDFModal, setShowAddPDFModal] = useState(false)
  const [selectedSection, setSelectedSection] = useState<string | null>(null)
  const [selectedVideo, setSelectedVideo] = useState<string | null>(null)

  // Mock data - will be replaced with real data from database
  const course = {
    id: courseId,
    title: 'مقدمة في البرمجة',
    description: 'تعلم أساسيات البرمجة باستخدام JavaScript',
    instructor: 'أحمد محمد',
    thumbnail: '/api/placeholder/400/300',
    isPublished: true,
    createdAt: '2024-01-15',
    sections: [
      {
        id: '1',
        title: 'الأساسيات',
        description: 'تعلم الأساسيات',
        orderIndex: 1,
        videos: [
          {
            id: '1',
            title: 'مقدمة عن البرمجة',
            description: 'فيديو تعريفي عن البرمجة',
            videoUrl: 'https://example.com/video1.mp4',
            duration: 600,
            orderIndex: 1,
            pdfs: [
              {
                id: '1',
                title: 'ملاحظات الدرس الأول',
                fileUrl: 'https://example.com/notes1.pdf',
                fileSize: 1024000
              }
            ]
          }
        ]
      }
    ]
  }

  const handleAddSection = (data: any) => {
    console.log('Adding section:', data)
    setShowAddSectionModal(false)
  }

  const handleAddVideo = (data: any) => {
    console.log('Adding video to section:', selectedSection, data)
    setShowAddVideoModal(false)
    setSelectedSection(null)
  }

  const handleAddPDF = (data: any) => {
    console.log('Adding PDF to video:', selectedVideo, data)
    setShowAddPDFModal(false)
    setSelectedVideo(null)
  }

  return (
    <ProtectedRoute requiredRole="admin">
      <Layout>
        <div className="space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4 rtl:space-x-reverse">
              <Button
                variant="outline"
                onClick={() => router.back()}
                className="flex items-center space-x-2 rtl:space-x-reverse"
              >
                <ArrowLeftIcon className="h-4 w-4" />
                <span>العودة</span>
              </Button>
              <div>
                <h1 className="text-2xl font-bold text-primary-900">{course.title}</h1>
                <p className="text-secondary-600">إدارة محتوى الكورس</p>
              </div>
            </div>
            <div className="flex items-center space-x-3 rtl:space-x-reverse">
              <Button
                variant="outline"
                onClick={() => router.push(`/student/courses/${course.id}`)}
                className="flex items-center space-x-2 rtl:space-x-reverse"
              >
                <EyeIcon className="h-4 w-4" />
                <span>معاينة</span>
              </Button>
              <Button
                onClick={() => setShowAddSectionModal(true)}
                className="flex items-center space-x-2 rtl:space-x-reverse"
              >
                <PlusIcon className="h-4 w-4" />
                <span>إضافة قسم</span>
              </Button>
            </div>
          </div>

          {/* Course Info */}
          <Card>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="md:col-span-2">
                <h2 className="text-lg font-semibold text-primary-900 mb-2">معلومات الكورس</h2>
                <p className="text-secondary-600 mb-4">{course.description}</p>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="font-medium text-secondary-700">المدرس:</span>
                    <span className="text-secondary-600 mr-2">{course.instructor}</span>
                  </div>
                  <div>
                    <span className="font-medium text-secondary-700">تاريخ الإنشاء:</span>
                    <span className="text-secondary-600 mr-2">{course.createdAt}</span>
                  </div>
                  <div>
                    <span className="font-medium text-secondary-700">الحالة:</span>
                    <span className={`mr-2 px-2 py-1 rounded-full text-xs ${
                      course.isPublished 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-yellow-100 text-yellow-800'
                    }`}>
                      {course.isPublished ? 'منشور' : 'مسودة'}
                    </span>
                  </div>
                </div>
              </div>
              <div>
                <img 
                  src={course.thumbnail} 
                  alt={course.title}
                  className="w-full h-32 object-cover rounded-lg"
                />
              </div>
            </div>
          </Card>

          {/* Course Sections */}
          <div className="space-y-6">
            {course.sections.map((section) => (
              <Card key={section.id}>
                <Card.Header>
                  <div className="flex justify-between items-center">
                    <Card.Title>{section.title}</Card.Title>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        setSelectedSection(section.id)
                        setShowAddVideoModal(true)
                      }}
                      className="flex items-center space-x-2 rtl:space-x-reverse"
                    >
                      <PlusIcon className="h-4 w-4" />
                      <span>إضافة فيديو</span>
                    </Button>
                  </div>
                  {section.description && (
                    <p className="text-secondary-600 mt-2">{section.description}</p>
                  )}
                </Card.Header>

                <Card.Content>
                  <div className="space-y-4">
                    {section.videos.map((video) => (
                      <div key={video.id} className="border border-secondary-200 rounded-lg p-4">
                        <div className="flex justify-between items-start mb-3">
                          <div className="flex-1">
                            <h4 className="font-medium text-primary-900">{video.title}</h4>
                            {video.description && (
                              <p className="text-secondary-600 text-sm mt-1">{video.description}</p>
                            )}
                            <div className="flex items-center space-x-4 rtl:space-x-reverse mt-2 text-sm text-secondary-500">
                              <span>المدة: {Math.floor(video.duration / 60)} دقيقة</span>
                              <span>الترتيب: {video.orderIndex}</span>
                            </div>
                          </div>
                          <div className="flex items-center space-x-2 rtl:space-x-reverse">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => {
                                setSelectedVideo(video.id)
                                setShowAddPDFModal(true)
                              }}
                            >
                              <DocumentTextIcon className="h-4 w-4" />
                            </Button>
                            <Button variant="outline" size="sm">
                              <PencilIcon className="h-4 w-4" />
                            </Button>
                            <Button variant="outline" size="sm">
                              <TrashIcon className="h-4 w-4" />
                            </Button>
                            <Button variant="outline" size="sm">
                              <PlayIcon className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>

                        {/* PDFs */}
                        {video.pdfs && video.pdfs.length > 0 && (
                          <div className="border-t border-secondary-200 pt-3">
                            <h5 className="text-sm font-medium text-secondary-700 mb-2">الملفات المرفقة:</h5>
                            <div className="space-y-2">
                              {video.pdfs.map((pdf) => (
                                <div key={pdf.id} className="flex items-center justify-between bg-secondary-50 p-2 rounded">
                                  <div className="flex items-center space-x-2 rtl:space-x-reverse">
                                    <DocumentTextIcon className="h-4 w-4 text-secondary-500" />
                                    <span className="text-sm text-secondary-700">{pdf.title}</span>
                                    <span className="text-xs text-secondary-500">
                                      ({(pdf.fileSize / 1024 / 1024).toFixed(1)} MB)
                                    </span>
                                  </div>
                                  <div className="flex items-center space-x-2 rtl:space-x-reverse">
                                    <Button variant="outline" size="sm">
                                      <PencilIcon className="h-4 w-4" />
                                    </Button>
                                    <Button variant="outline" size="sm">
                                      <TrashIcon className="h-4 w-4" />
                                    </Button>
                                  </div>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </Card.Content>
              </Card>
            ))}
          </div>
        </div>

        {/* Add Section Modal */}
        <Modal
          isOpen={showAddSectionModal}
          onClose={() => setShowAddSectionModal(false)}
          title="إضافة قسم جديد"
        >
          <form onSubmit={(e) => {
            e.preventDefault()
            const formData = new FormData(e.currentTarget)
            handleAddSection({
              title: formData.get('title'),
              description: formData.get('description')
            })
          }}>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-secondary-700 mb-1">
                  عنوان القسم
                </label>
                <input
                  type="text"
                  name="title"
                  required
                  className="w-full px-3 py-2 border border-secondary-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-secondary-700 mb-1">
                  وصف القسم (اختياري)
                </label>
                <textarea
                  name="description"
                  rows={3}
                  className="w-full px-3 py-2 border border-secondary-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                />
              </div>
            </div>
            <div className="flex justify-end space-x-3 rtl:space-x-reverse mt-6">
              <Button
                type="button"
                variant="outline"
                onClick={() => setShowAddSectionModal(false)}
              >
                إلغاء
              </Button>
              <Button type="submit">
                إضافة القسم
              </Button>
            </div>
          </form>
        </Modal>

        {/* Add Video Modal */}
        <Modal
          isOpen={showAddVideoModal}
          onClose={() => {
            setShowAddVideoModal(false)
            setSelectedSection(null)
          }}
          title="إضافة فيديو جديد"
        >
          <form onSubmit={(e) => {
            e.preventDefault()
            const formData = new FormData(e.currentTarget)
            handleAddVideo({
              title: formData.get('title'),
              description: formData.get('description'),
              videoFile: formData.get('video')
            })
          }}>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-secondary-700 mb-1">
                  عنوان الفيديو
                </label>
                <input
                  type="text"
                  name="title"
                  required
                  className="w-full px-3 py-2 border border-secondary-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-secondary-700 mb-1">
                  وصف الفيديو (اختياري)
                </label>
                <textarea
                  name="description"
                  rows={3}
                  className="w-full px-3 py-2 border border-secondary-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-secondary-700 mb-1">
                  ملف الفيديو
                </label>
                <FileUpload
                  accept={{ 'video/*': ['.mp4', '.webm', '.ogg'] }}
                  onFileSelect={(files) => console.log('Video selected:', files)}
                />
              </div>
            </div>
            <div className="flex justify-end space-x-3 rtl:space-x-reverse mt-6">
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  setShowAddVideoModal(false)
                  setSelectedSection(null)
                }}
              >
                إلغاء
              </Button>
              <Button type="submit">
                إضافة الفيديو
              </Button>
            </div>
          </form>
        </Modal>

        {/* Add PDF Modal */}
        <Modal
          isOpen={showAddPDFModal}
          onClose={() => {
            setShowAddPDFModal(false)
            setSelectedVideo(null)
          }}
          title="إضافة ملف PDF"
        >
          <form onSubmit={(e) => {
            e.preventDefault()
            const formData = new FormData(e.currentTarget)
            handleAddPDF({
              title: formData.get('title'),
              pdfFile: formData.get('pdf')
            })
          }}>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-secondary-700 mb-1">
                  عنوان الملف
                </label>
                <input
                  type="text"
                  name="title"
                  required
                  className="w-full px-3 py-2 border border-secondary-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-secondary-700 mb-1">
                  ملف PDF
                </label>
                <FileUpload
                  accept={{ 'application/pdf': ['.pdf'] }}
                  onFileSelect={(files) => console.log('PDF selected:', files)}
                />
              </div>
            </div>
            <div className="flex justify-end space-x-3 rtl:space-x-reverse mt-6">
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  setShowAddPDFModal(false)
                  setSelectedVideo(null)
                }}
              >
                إلغاء
              </Button>
              <Button type="submit">
                إضافة الملف
              </Button>
            </div>
          </form>
        </Modal>
      </Layout>
    </ProtectedRoute>
  )
}
