{"functions": {"source": "functions", "runtime": "nodejs18"}, "hosting": {"public": ".next/out", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "rewrites": [{"source": "**", "function": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "headers": [{"source": "**/*.@(js|css)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "**/*.@(jpg|jpeg|gif|png|svg|webp|ico)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}]}}