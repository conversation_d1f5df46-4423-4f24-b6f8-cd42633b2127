// Mock authentication for demo purposes
export interface MockUser {
  id: string
  email: string
  full_name: string
  role: 'admin' | 'student'
}

// Mock users database
const mockUsers: MockUser[] = [
  {
    id: 'admin-1',
    email: '<EMAIL>',
    full_name: 'مدير النظام',
    role: 'admin'
  },
  {
    id: 'admin-2', 
    email: '<EMAIL>',
    full_name: 'أحمد محمد',
    role: 'admin'
  },
  {
    id: 'student-1',
    email: '<EMAIL>',
    full_name: 'محمد علي',
    role: 'student'
  },
  {
    id: 'student-2',
    email: '<EMAIL>', 
    full_name: 'فاطمة أحمد',
    role: 'student'
  }
]

// Mock passwords (in real app, these would be hashed)
const mockPasswords: Record<string, string> = {
  '<EMAIL>': 'admin123',
  '<EMAIL>': 'admin123',
  '<EMAIL>': 'student123',
  '<EMAIL>': 'student123'
}

// Mock authentication functions
export const mockSignIn = async (email: string, password: string): Promise<{ user: MockUser | null, error: string | null }> => {
  // Simulate network delay
  await new Promise(resolve => setTimeout(resolve, 1000))
  
  const user = mockUsers.find(u => u.email === email)
  const correctPassword = mockPasswords[email]
  
  if (!user) {
    return { user: null, error: 'المستخدم غير موجود' }
  }
  
  if (password !== correctPassword) {
    return { user: null, error: 'كلمة المرور غير صحيحة' }
  }
  
  // Store user in localStorage for persistence
  localStorage.setItem('mockUser', JSON.stringify(user))

  // Trigger storage event for other tabs/components
  window.dispatchEvent(new Event('storage'))

  return { user, error: null }
}

export const mockSignOut = async (): Promise<{ error: string | null }> => {
  localStorage.removeItem('mockUser')
  // Trigger storage event for other tabs/components
  window.dispatchEvent(new Event('storage'))
  return { error: null }
}

export const mockGetCurrentUser = (): MockUser | null => {
  const stored = localStorage.getItem('mockUser')
  if (stored) {
    try {
      return JSON.parse(stored)
    } catch {
      return null
    }
  }
  return null
}

export const mockSignUp = async (
  email: string, 
  password: string, 
  fullName: string, 
  role: 'student' | 'admin'
): Promise<{ user: MockUser | null, error: string | null }> => {
  // Simulate network delay
  await new Promise(resolve => setTimeout(resolve, 1000))
  
  // Check if user already exists
  const existingUser = mockUsers.find(u => u.email === email)
  if (existingUser) {
    return { user: null, error: 'المستخدم موجود بالفعل' }
  }
  
  // Create new user
  const newUser: MockUser = {
    id: `${role}-${Date.now()}`,
    email,
    full_name: fullName,
    role
  }
  
  // Add to mock database
  mockUsers.push(newUser)
  mockPasswords[email] = password
  
  // Store user in localStorage
  localStorage.setItem('mockUser', JSON.stringify(newUser))
  
  return { user: newUser, error: null }
}

// Available demo accounts
export const getDemoAccounts = () => {
  return [
    {
      email: '<EMAIL>',
      password: 'admin123',
      role: 'admin',
      name: 'مدير النظام'
    },
    {
      email: '<EMAIL>', 
      password: 'student123',
      role: 'student',
      name: 'محمد علي'
    }
  ]
}
