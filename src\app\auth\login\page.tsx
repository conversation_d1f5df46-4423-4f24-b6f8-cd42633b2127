'use client'

import { useState, useEffect } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import Link from 'next/link'
import { signIn } from '@/lib/auth'
import { mockSignIn, getDemoAccounts } from '@/lib/mockAuth'
import { useAuth } from '@/contexts/AuthContext'
import { EyeIcon, EyeSlashIcon } from '@heroicons/react/24/outline'
import toast, { Toaster } from 'react-hot-toast'

export default function LoginPage() {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [showPassword, setShowPassword] = useState(false)
  const [loading, setLoading] = useState(false)
  const router = useRouter()
  const searchParams = useSearchParams()
  const { user, refreshUser } = useAuth()
  
  const role = searchParams.get('role') || 'student'

  useEffect(() => {
    if (user) {
      // Redirect based on user role
      if (user.role === 'admin') {
        router.push('/admin/dashboard')
      } else {
        router.push('/student/dashboard')
      }
    }
  }, [user, router])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      // Use mock authentication for demo
      const { user, error } = await mockSignIn(email, password)

      if (error) {
        toast.error(error)
        return
      }

      if (user) {
        toast.success('تم تسجيل الدخول بنجاح!')
        // Refresh auth context
        refreshUser()
        // Small delay to ensure context is updated
        setTimeout(() => {
          if (user.role === 'admin') {
            router.push('/admin/dashboard')
          } else {
            router.push('/student/dashboard')
          }
        }, 100)
      }
    } catch (error) {
      toast.error('حدث خطأ أثناء تسجيل الدخول')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 to-white flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <Toaster position="top-center" />
      
      <div className="max-w-md w-full space-y-8">
        <div>
          <h2 className="mt-6 text-center text-3xl font-bold text-primary-900">
            تسجيل الدخول
          </h2>
          <p className="mt-2 text-center text-sm text-secondary-600">
            {role === 'admin' ? 'دخول المدرسين' : 'دخول الطلاب'}
          </p>
        </div>

        {/* Demo Accounts */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
          <h3 className="text-sm font-medium text-blue-900 mb-3">🎯 حسابات تجريبية للاختبار:</h3>
          <div className="space-y-2 text-xs">
            {getDemoAccounts().map((account, index) => (
              <div key={index} className="bg-white rounded p-2 border">
                <div className="flex justify-between items-center">
                  <div>
                    <span className="font-medium text-gray-700">{account.name}</span>
                    <span className="text-blue-600 mx-2">({account.role === 'admin' ? 'مدير' : 'طالب'})</span>
                  </div>
                  <button
                    type="button"
                    onClick={() => {
                      setEmail(account.email)
                      setPassword(account.password)
                    }}
                    className="text-blue-600 hover:text-blue-800 text-xs underline"
                  >
                    استخدام
                  </button>
                </div>
                <div className="text-gray-500 mt-1">
                  {account.email} / {account.password}
                </div>
              </div>
            ))}
          </div>
        </div>

        <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
          <div className="rounded-md shadow-sm space-y-4">
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-secondary-700 mb-1">
                البريد الإلكتروني
              </label>
              <input
                id="email"
                name="email"
                type="email"
                autoComplete="email"
                required
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="appearance-none relative block w-full px-3 py-2 border border-secondary-300 placeholder-secondary-500 text-secondary-900 rounded-lg focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm"
                placeholder="أدخل بريدك الإلكتروني"
              />
            </div>
            
            <div>
              <label htmlFor="password" className="block text-sm font-medium text-secondary-700 mb-1">
                كلمة المرور
              </label>
              <div className="relative">
                <input
                  id="password"
                  name="password"
                  type={showPassword ? 'text' : 'password'}
                  autoComplete="current-password"
                  required
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="appearance-none relative block w-full px-3 py-2 pr-10 border border-secondary-300 placeholder-secondary-500 text-secondary-900 rounded-lg focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm"
                  placeholder="أدخل كلمة المرور"
                />
                <button
                  type="button"
                  className="absolute inset-y-0 right-0 pr-3 flex items-center"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? (
                    <EyeSlashIcon className="h-5 w-5 text-secondary-400" />
                  ) : (
                    <EyeIcon className="h-5 w-5 text-secondary-400" />
                  )}
                </button>
              </div>
            </div>
          </div>

          <div>
            <button
              type="submit"
              disabled={loading}
              className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-lg text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? 'جاري تسجيل الدخول...' : 'تسجيل الدخول'}
            </button>
          </div>

          <div className="text-center">
            <p className="text-sm text-secondary-600">
              ليس لديك حساب؟{' '}
              <Link 
                href={`/auth/register?role=${role}`}
                className="font-medium text-primary-600 hover:text-primary-500"
              >
                إنشاء حساب جديد
              </Link>
            </p>
          </div>

          <div className="text-center">
            <Link 
              href={role === 'admin' ? '/auth/login?role=student' : '/auth/login?role=admin'}
              className="text-sm text-secondary-600 hover:text-primary-600"
            >
              {role === 'admin' ? 'دخول كطالب' : 'دخول كمدرس'}
            </Link>
          </div>
        </form>
      </div>
    </div>
  )
}
